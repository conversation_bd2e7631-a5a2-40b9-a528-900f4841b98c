#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证转换后的CSV文件是否正确无乱码
"""

import pandas as pd

def verify_files():
    print("🔍 验证转换后的文件...")
    print("=" * 50)
    
    # 验证比赛结果文件
    print("📊 验证比赛结果文件 (football_matches_utf8.csv)")
    try:
        df_matches = pd.read_csv('football_matches_utf8.csv', encoding='utf-8')
        print(f"✅ 文件读取成功")
        print(f"📈 数据形状: {df_matches.shape}")
        print(f"📋 列名: {df_matches.columns.tolist()[:5]}...")  # 只显示前5个列名
        print(f"🏆 参赛队伍示例: {df_matches['参赛队伍1'].unique()[:5].tolist()}")
        print(f"⚽ 已完成轮次: {df_matches['轮次'].unique().tolist()}")
        print()
    except Exception as e:
        print(f"❌ 读取失败: {e}")
        return False
    
    # 验证赛程文件
    print("📅 验证赛程文件 (matches_utf8.csv)")
    try:
        df_schedule = pd.read_csv('matches_utf8.csv', encoding='utf-8')
        print(f"✅ 文件读取成功")
        print(f"📈 数据形状: {df_schedule.shape}")
        print(f"📋 列名: {df_schedule.columns.tolist()}")
        print(f"🏟️ 主队示例: {df_schedule['HomeTeam（主队）'].unique()[:5].tolist()}")
        print(f"📆 赛程轮次: {df_schedule['Round（轮次）'].unique().tolist()}")
        print()
    except Exception as e:
        print(f"❌ 读取失败: {e}")
        return False
    
    print("🎉 所有文件验证通过！文件已成功转换为UTF-8编码，无乱码问题。")
    print()
    print("📁 转换后的文件:")
    print("   • football_matches_utf8.csv - 比赛结果数据")
    print("   • matches_utf8.csv - 赛程安排数据")
    print()
    print("💡 现在你可以用任何支持UTF-8的软件打开这些文件，不会再有乱码问题！")
    
    return True

if __name__ == "__main__":
    verify_files()
