#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BTD模型留一法验证
每次留出一场比赛，用其余比赛训练，预测这一场
"""

import pandas as pd
import numpy as np
import pymc as pm
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
import warnings
warnings.filterwarnings('ignore')

class BTDLeaveOneOut:
    """BTD模型留一法验证"""
    
    def __init__(self):
        self.teams = []
        self.team_to_idx = {}
        
    def load_data(self):
        """加载数据"""
        print("📊 加载比赛数据...")
        
        try:
            df_results = pd.read_csv('maches2.csv', encoding='utf-8')
            print("✅ 成功读取 maches2.csv")
        except:
            try:
                df_results = pd.read_csv('合并后的比赛数据.csv', encoding='utf-8')
                print("✅ 成功读取 合并后的比赛数据.csv")
            except Exception as e:
                print(f"❌ 文件读取失败: {e}")
                return []
        
        matches = []
        teams = set()
        
        for _, row in df_results.iterrows():
            # 尝试不同的列名组合
            home_team_cols = ['参赛队伍1', 'home_team', '主队']
            away_team_cols = ['参赛队伍2', 'away_team', '客队']
            home_goals_cols = ['参赛队伍1进球数', '参赛队伍1进球 数', 'home_goals']
            away_goals_cols = ['参赛队伍2进球数', 'away_goals']
            round_cols = ['轮次', 'round', '第几轮']
            
            home_team = None
            away_team = None
            home_goals = None
            away_goals = None
            round_num = None
            
            # 查找各字段
            for col in home_team_cols:
                if col in row and pd.notna(row[col]):
                    home_team = row[col]
                    break
            
            for col in away_team_cols:
                if col in row and pd.notna(row[col]):
                    away_team = row[col]
                    break
            
            for col in home_goals_cols:
                if col in row and pd.notna(row[col]):
                    home_goals = int(row[col])
                    break
            
            for col in away_goals_cols:
                if col in row and pd.notna(row[col]):
                    away_goals = int(row[col])
                    break
            
            for col in round_cols:
                if col in row and pd.notna(row[col]):
                    round_str = str(row[col])
                    try:
                        # 处理各种轮次格式
                        if '第' in round_str and '轮' in round_str:
                            round_num = int(round_str.replace('第', '').replace('轮', ''))
                        elif '(' in round_str:
                            round_num = int(round_str.split('(')[0])
                        else:
                            round_num = int(round_str)
                        break
                    except ValueError:
                        continue
            
            if all(x is not None for x in [home_team, away_team, home_goals, away_goals]):
                teams.add(home_team)
                teams.add(away_team)
                
                if home_goals > away_goals:
                    result = 1  # 主胜
                elif home_goals < away_goals:
                    result = -1  # 主负
                else:
                    result = 0  # 平局
                
                matches.append({
                    'round': round_num if round_num is not None else 0,
                    'home_team': home_team,
                    'away_team': away_team,
                    'home_goals': home_goals,
                    'away_goals': away_goals,
                    'result': result
                })
        
        self.teams = sorted(list(teams))
        self.team_to_idx = {team: i for i, team in enumerate(self.teams)}
        
        print(f"队伍数量: {len(self.teams)}")
        print(f"比赛数量: {len(matches)}")
        
        # 统计结果分布
        results = [m['result'] for m in matches]
        home_wins = sum(1 for r in results if r == 1)
        draws = sum(1 for r in results if r == 0)
        away_wins = sum(1 for r in results if r == -1)
        
        print(f"结果分布: 主胜{home_wins}场({home_wins/len(matches):.1%}), "
              f"平局{draws}场({draws/len(matches):.1%}), "
              f"客胜{away_wins}场({away_wins/len(matches):.1%})")
        
        return matches
    
    def calculate_prior_strengths(self, matches):
        """基于训练数据计算先验强度"""
        team_stats = {team: {'wins': 0, 'draws': 0, 'losses': 0, 'games': 0} for team in self.teams}
        
        for match in matches:
            home_team = match['home_team']
            away_team = match['away_team']
            result = match['result']
            
            team_stats[home_team]['games'] += 1
            team_stats[away_team]['games'] += 1
            
            if result == 1:  # 主胜
                team_stats[home_team]['wins'] += 1
                team_stats[away_team]['losses'] += 1
            elif result == -1:  # 主负
                team_stats[away_team]['wins'] += 1
                team_stats[home_team]['losses'] += 1
            else:  # 平局
                team_stats[home_team]['draws'] += 1
                team_stats[away_team]['draws'] += 1
        
        prior_strengths = []
        for team in self.teams:
            stats = team_stats[team]
            if stats['games'] > 0:
                win_rate = (stats['wins'] + 0.5 * stats['draws']) / stats['games']
                strength = max(win_rate * 2 + 0.1, 0.1)
            else:
                strength = 1.0
            prior_strengths.append(strength)
        
        return np.array(prior_strengths)
    
    def fit_btd_model_fast(self, train_matches):
        """快速拟合BTD模型（用于留一法）"""
        prior_strengths = self.calculate_prior_strengths(train_matches)
        n_teams = len(self.teams)
        
        # 准备训练数据
        home_teams = np.array([self.team_to_idx[m['home_team']] for m in train_matches])
        away_teams = np.array([self.team_to_idx[m['away_team']] for m in train_matches])
        results = np.array([m['result'] for m in train_matches])
        
        try:
            with pm.Model() as model:
                # 队伍强度参数
                log_alpha = pm.Normal('log_alpha', 
                                    mu=np.log(prior_strengths), 
                                    sigma=0.5,
                                    shape=n_teams)
                alpha = pm.Deterministic('alpha', pm.math.exp(log_alpha))
                
                # 平局参数
                gamma = pm.Gamma('gamma', alpha=2.0, beta=2.0)
                
                # 主场优势
                home_advantage = pm.Normal('home_advantage', mu=0.1, sigma=0.1)
                
                # BTD概率计算
                alpha_home = alpha[home_teams] * pm.math.exp(home_advantage)
                alpha_away = alpha[away_teams]
                total = alpha_home + alpha_away + gamma
                
                p_home_win = alpha_home / total
                p_draw = gamma / total
                p_away_win = alpha_away / total
                
                # 似然函数
                categorical_results = np.where(results == -1, 0, np.where(results == 0, 1, 2))
                probs_matrix = pm.math.stack([p_away_win, p_draw, p_home_win], axis=1)
                
                likelihood = pm.Categorical('likelihood', p=probs_matrix, observed=categorical_results)
            
            # 快速采样
            with model:
                trace = pm.sample(
                    draws=600,  # 减少采样数以加快速度
                    tune=400,
                    chains=2,
                    cores=1,
                    random_seed=42,
                    return_inferencedata=True,
                    progressbar=False  # 关闭进度条
                )
            
            return trace
            
        except Exception as e:
            print(f"模型拟合失败: {e}")
            return None
    
    def predict_match(self, home_team, away_team, trace):
        """预测单场比赛"""
        if trace is None:
            return None
        
        try:
            home_idx = self.team_to_idx[home_team]
            away_idx = self.team_to_idx[away_team]
            
            # 从后验分布中采样
            alpha_samples = trace.posterior['alpha'].values.reshape(-1, len(self.teams))
            gamma_samples = trace.posterior['gamma'].values.flatten()
            home_adv_samples = trace.posterior['home_advantage'].values.flatten()
            
            # 计算预测概率
            alpha_home = alpha_samples[:, home_idx] * np.exp(home_adv_samples)
            alpha_away = alpha_samples[:, away_idx]
            total = alpha_home + alpha_away + gamma_samples
            
            p_home_win = np.mean(alpha_home / total)
            p_draw = np.mean(gamma_samples / total)
            p_away_win = np.mean(alpha_away / total)
            
            # 归一化
            total_prob = p_home_win + p_draw + p_away_win
            if total_prob > 0:
                p_home_win /= total_prob
                p_draw /= total_prob
                p_away_win /= total_prob
            else:
                return None
            
            return {
                'p_home_win': p_home_win,
                'p_draw': p_draw,
                'p_away_win': p_away_win,
                'predicted_result': np.argmax([p_away_win, p_draw, p_home_win])
            }
            
        except Exception as e:
            print(f"预测失败: {e}")
            return None
    
    def run_leave_one_out(self):
        """运行留一法验证"""
        print("🎯 BTD模型留一法验证")
        print("="*50)
        
        # 1. 加载数据
        matches = self.load_data()
        if not matches:
            return
        
        n_matches = len(matches)
        print(f"\n🔄 开始留一法验证，共需要训练{n_matches}次模型...")
        
        predictions = []
        actuals = []
        successful_predictions = 0
        failed_predictions = 0
        
        print("\n逐场留一法预测:")
        print("序号 | 对阵 | 实际 | 预测 | 状态")
        print("-" * 50)
        
        for i, test_match in enumerate(matches):
            # 创建训练集（除了当前比赛）
            train_matches = matches[:i] + matches[i+1:]
            
            # 训练模型
            trace = self.fit_btd_model_fast(train_matches)
            
            if trace is not None:
                # 预测当前比赛
                pred = self.predict_match(test_match['home_team'], test_match['away_team'], trace)
                
                if pred is not None:
                    predictions.append(pred['predicted_result'])
                    successful_predictions += 1
                    
                    # 实际结果
                    result = test_match['result']
                    if result == 1:
                        actuals.append(2)  # 主胜
                        actual_str = "主胜"
                    elif result == -1:
                        actuals.append(0)  # 客胜
                        actual_str = "客胜"
                    else:
                        actuals.append(1)  # 平局
                        actual_str = "平局"
                    
                    # 预测结果
                    pred_labels = ['客胜', '平局', '主胜']
                    pred_str = pred_labels[pred['predicted_result']]
                    
                    # 是否预测正确
                    correct = "✅" if pred['predicted_result'] == actuals[-1] else "❌"
                    
                    print(f"{i+1:2d}   | {test_match['home_team']} vs {test_match['away_team']} | "
                          f"{actual_str} | {pred_str} {correct} | 成功")
                else:
                    failed_predictions += 1
                    print(f"{i+1:2d}   | {test_match['home_team']} vs {test_match['away_team']} | "
                          f"? | ? | 预测失败")
            else:
                failed_predictions += 1
                print(f"{i+1:2d}   | {test_match['home_team']} vs {test_match['away_team']} | "
                      f"? | ? | 训练失败")
        
        print(f"\n📊 留一法验证结果:")
        print(f"成功预测: {successful_predictions}场")
        print(f"失败预测: {failed_predictions}场")
        print(f"成功率: {successful_predictions/n_matches:.1%}")
        
        if len(predictions) > 0 and len(actuals) > 0:
            # 计算准确率
            accuracy = accuracy_score(actuals, predictions)
            print(f"\n📈 预测准确率: {accuracy:.3f} ({accuracy:.1%})")
            
            print("\n分类报告:")
            print(classification_report(actuals, predictions, 
                                      target_names=['客胜', '平局', '主胜']))
            
            print("混淆矩阵:")
            cm = confusion_matrix(actuals, predictions)
            print(cm)
            
            # 分析预测错误
            print(f"\n🔍 错误分析:")
            errors = []
            for i, (actual, pred) in enumerate(zip(actuals, predictions)):
                if actual != pred:
                    match_idx = [j for j, m in enumerate(matches) if j < len(actuals)][i]
                    match = matches[match_idx]
                    errors.append({
                        'match': match,
                        'actual': ['客胜', '平局', '主胜'][actual],
                        'predicted': ['客胜', '平局', '主胜'][pred]
                    })
            
            print(f"预测错误的比赛数: {len(errors)}")
            for j, error in enumerate(errors[:10]):  # 显示前10个错误
                match = error['match']
                print(f"  {j+1}. {match['home_team']} vs {match['away_team']}: "
                      f"实际{error['actual']}, 预测{error['predicted']}")
            
            return accuracy
        else:
            print("❌ 没有成功的预测，无法计算准确率")
            return 0.0

def main():
    """主函数"""
    validator = BTDLeaveOneOut()
    accuracy = validator.run_leave_one_out()
    
    print(f"\n💡 留一法验证总结:")
    print(f"✅ 留一法准确率: {accuracy:.1%}")
    print(f"✅ 这是最严格的交叉验证方法")
    print(f"✅ 每次预测都基于完全独立的训练集")
    print(f"✅ 最能反映模型的泛化能力")

if __name__ == "__main__":
    main()
