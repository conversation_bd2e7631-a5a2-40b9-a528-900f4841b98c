#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BTD模型时间序列验证
用前70%数据训练，后30%数据测试
"""

import pandas as pd
import numpy as np
import pymc as pm
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
import warnings
warnings.filterwarnings('ignore')

class BTDTemporalValidation:
    """BTD模型时间序列验证"""
    
    def __init__(self):
        self.teams = []
        self.team_to_idx = {}
        self.trace = None
        
    def load_and_split_data(self):
        """加载数据并按时间分割"""
        print("📊 加载比赛数据...")
        
        try:
            df_results = pd.read_csv('maches2.csv', encoding='utf-8')
            print("✅ 成功读取 maches2.csv")
        except:
            try:
                df_results = pd.read_csv('合并后的比赛数据.csv', encoding='utf-8')
                print("✅ 成功读取 合并后的比赛数据.csv")
            except Exception as e:
                print(f"❌ 文件读取失败: {e}")
                return [], []
        
        matches = []
        teams = set()
        
        print("数据列名:", df_results.columns.tolist())
        
        for _, row in df_results.iterrows():
            # 尝试不同的列名组合
            home_team_cols = ['参赛队伍1', 'home_team', '主队']
            away_team_cols = ['参赛队伍2', 'away_team', '客队']
            home_goals_cols = ['参赛队伍1进球数', '参赛队伍1进球 数', 'home_goals']
            away_goals_cols = ['参赛队伍2进球数', 'away_goals']
            round_cols = ['轮次', 'round', '第几轮']
            
            home_team = None
            away_team = None
            home_goals = None
            away_goals = None
            round_num = None
            
            # 查找各字段
            for col in home_team_cols:
                if col in row and pd.notna(row[col]):
                    home_team = row[col]
                    break
            
            for col in away_team_cols:
                if col in row and pd.notna(row[col]):
                    away_team = row[col]
                    break
            
            for col in home_goals_cols:
                if col in row and pd.notna(row[col]):
                    home_goals = int(row[col])
                    break
            
            for col in away_goals_cols:
                if col in row and pd.notna(row[col]):
                    away_goals = int(row[col])
                    break
            
            for col in round_cols:
                if col in row and pd.notna(row[col]):
                    round_str = str(row[col])
                    try:
                        # 处理各种轮次格式
                        if '第' in round_str and '轮' in round_str:
                            # "第X轮"格式
                            round_num = int(round_str.replace('第', '').replace('轮', ''))
                        elif '(' in round_str:
                            # "4(补赛)"格式，取括号前的数字
                            round_num = int(round_str.split('(')[0])
                        else:
                            # 直接是数字
                            round_num = int(round_str)
                        break
                    except ValueError:
                        print(f"无法解析轮次: '{round_str}'")
                        continue
            
            if all(x is not None for x in [home_team, away_team, home_goals, away_goals, round_num]):
                teams.add(home_team)
                teams.add(away_team)
                
                if home_goals > away_goals:
                    result = 1  # 主胜
                elif home_goals < away_goals:
                    result = -1  # 主负
                else:
                    result = 0  # 平局
                
                matches.append({
                    'round': round_num,
                    'home_team': home_team,
                    'away_team': away_team,
                    'home_goals': home_goals,
                    'away_goals': away_goals,
                    'result': result
                })
        
        # 按轮次排序
        matches.sort(key=lambda x: x['round'])
        
        self.teams = sorted(list(teams))
        self.team_to_idx = {team: i for i, team in enumerate(self.teams)}
        
        print(f"队伍数量: {len(self.teams)}")
        print(f"比赛数量: {len(matches)}")
        print(f"轮次范围: {matches[0]['round']} - {matches[-1]['round']}")
        
        # 按70%-30%分割
        split_point = int(len(matches) * 0.7)
        train_matches = matches[:split_point]
        test_matches = matches[split_point:]
        
        print(f"\n📈 数据分割:")
        print(f"训练集: {len(train_matches)}场比赛 (前{train_matches[-1]['round']}轮)")
        print(f"测试集: {len(test_matches)}场比赛 (第{test_matches[0]['round']}-{test_matches[-1]['round']}轮)")
        
        # 统计训练集结果分布
        train_results = [m['result'] for m in train_matches]
        train_home_wins = sum(1 for r in train_results if r == 1)
        train_draws = sum(1 for r in train_results if r == 0)
        train_away_wins = sum(1 for r in train_results if r == -1)
        
        print(f"\n训练集结果分布:")
        print(f"主胜{train_home_wins}场({train_home_wins/len(train_matches):.1%}), "
              f"平局{train_draws}场({train_draws/len(train_matches):.1%}), "
              f"客胜{train_away_wins}场({train_away_wins/len(train_matches):.1%})")
        
        # 统计测试集结果分布
        test_results = [m['result'] for m in test_matches]
        test_home_wins = sum(1 for r in test_results if r == 1)
        test_draws = sum(1 for r in test_results if r == 0)
        test_away_wins = sum(1 for r in test_results if r == -1)
        
        print(f"\n测试集结果分布:")
        print(f"主胜{test_home_wins}场({test_home_wins/len(test_matches):.1%}), "
              f"平局{test_draws}场({test_draws/len(test_matches):.1%}), "
              f"客胜{test_away_wins}场({test_away_wins/len(test_matches):.1%})")
        
        return train_matches, test_matches
    
    def calculate_prior_strengths(self, matches):
        """基于训练数据计算先验强度"""
        team_stats = {team: {'wins': 0, 'draws': 0, 'losses': 0, 'games': 0} for team in self.teams}
        
        for match in matches:
            home_team = match['home_team']
            away_team = match['away_team']
            result = match['result']
            
            team_stats[home_team]['games'] += 1
            team_stats[away_team]['games'] += 1
            
            if result == 1:  # 主胜
                team_stats[home_team]['wins'] += 1
                team_stats[away_team]['losses'] += 1
            elif result == -1:  # 主负
                team_stats[away_team]['wins'] += 1
                team_stats[home_team]['losses'] += 1
            else:  # 平局
                team_stats[home_team]['draws'] += 1
                team_stats[away_team]['draws'] += 1
        
        prior_strengths = []
        print(f"\n📊 基于训练集的队伍实力估计:")
        for team in self.teams:
            stats = team_stats[team]
            if stats['games'] > 0:
                win_rate = (stats['wins'] + 0.5 * stats['draws']) / stats['games']
                strength = max(win_rate * 2 + 0.1, 0.1)
                print(f"{team}: {stats['games']}场, 胜率{win_rate:.2f}, 强度{strength:.2f}")
            else:
                strength = 1.0
                print(f"{team}: 无比赛数据, 默认强度{strength:.2f}")
            prior_strengths.append(strength)
        
        return np.array(prior_strengths)
    
    def fit_btd_model(self, train_matches):
        """在训练集上拟合BTD模型"""
        print("\n🔥 在训练集上拟合BTD模型...")
        
        prior_strengths = self.calculate_prior_strengths(train_matches)
        n_teams = len(self.teams)
        
        # 准备训练数据
        home_teams = np.array([self.team_to_idx[m['home_team']] for m in train_matches])
        away_teams = np.array([self.team_to_idx[m['away_team']] for m in train_matches])
        results = np.array([m['result'] for m in train_matches])
        
        with pm.Model() as model:
            # 队伍强度参数
            log_alpha = pm.Normal('log_alpha', 
                                mu=np.log(prior_strengths), 
                                sigma=0.5,
                                shape=n_teams)
            alpha = pm.Deterministic('alpha', pm.math.exp(log_alpha))
            
            # 平局参数
            gamma = pm.Gamma('gamma', alpha=2.0, beta=2.0)
            
            # 主场优势
            home_advantage = pm.Normal('home_advantage', mu=0.1, sigma=0.1)
            
            # BTD概率计算
            alpha_home = alpha[home_teams] * pm.math.exp(home_advantage)
            alpha_away = alpha[away_teams]
            total = alpha_home + alpha_away + gamma
            
            p_home_win = alpha_home / total
            p_draw = gamma / total
            p_away_win = alpha_away / total
            
            # 似然函数
            categorical_results = np.where(results == -1, 0, np.where(results == 0, 1, 2))
            probs_matrix = pm.math.stack([p_away_win, p_draw, p_home_win], axis=1)
            
            likelihood = pm.Categorical('likelihood', p=probs_matrix, observed=categorical_results)
        
        # 采样
        with model:
            self.trace = pm.sample(
                draws=1200,
                tune=800,
                chains=2,
                cores=1,
                random_seed=42,
                return_inferencedata=True,
                progressbar=True
            )
        
        print("✅ BTD模型训练完成")
        return self.trace
    
    def predict_match(self, home_team, away_team):
        """预测单场比赛"""
        if self.trace is None:
            raise ValueError("模型尚未训练")
        
        home_idx = self.team_to_idx[home_team]
        away_idx = self.team_to_idx[away_team]
        
        # 从后验分布中采样
        alpha_samples = self.trace.posterior['alpha'].values.reshape(-1, len(self.teams))
        gamma_samples = self.trace.posterior['gamma'].values.flatten()
        home_adv_samples = self.trace.posterior['home_advantage'].values.flatten()
        
        # 计算预测概率
        alpha_home = alpha_samples[:, home_idx] * np.exp(home_adv_samples)
        alpha_away = alpha_samples[:, away_idx]
        total = alpha_home + alpha_away + gamma_samples
        
        p_home_win = np.mean(alpha_home / total)
        p_draw = np.mean(gamma_samples / total)
        p_away_win = np.mean(alpha_away / total)
        
        # 归一化
        total_prob = p_home_win + p_draw + p_away_win
        p_home_win /= total_prob
        p_draw /= total_prob
        p_away_win /= total_prob
        
        return {
            'p_home_win': p_home_win,
            'p_draw': p_draw,
            'p_away_win': p_away_win,
            'predicted_result': np.argmax([p_away_win, p_draw, p_home_win])
        }
    
    def evaluate_on_test_set(self, test_matches):
        """在测试集上评估模型"""
        print("\n📊 在测试集上评估模型...")
        
        predictions = []
        probabilities = []
        actuals = []
        
        print("逐场预测结果:")
        print("轮次 | 对阵 | 实际 | 预测 | 概率分布")
        print("-" * 60)
        
        for match in test_matches:
            # 预测
            pred = self.predict_match(match['home_team'], match['away_team'])
            predictions.append(pred['predicted_result'])
            probabilities.append(pred)
            
            # 实际结果
            result = match['result']
            if result == 1:
                actuals.append(2)  # 主胜
                actual_str = "主胜"
            elif result == -1:
                actuals.append(0)  # 客胜
                actual_str = "客胜"
            else:
                actuals.append(1)  # 平局
                actual_str = "平局"
            
            # 预测结果
            pred_labels = ['客胜', '平局', '主胜']
            pred_str = pred_labels[pred['predicted_result']]
            
            # 是否预测正确
            correct = "✅" if pred['predicted_result'] == actuals[-1] else "❌"
            
            print(f"{match['round']:2d}轮 | {match['home_team']} vs {match['away_team']} | "
                  f"{actual_str} | {pred_str} {correct} | "
                  f"主{pred['p_home_win']:.2f} 平{pred['p_draw']:.2f} 客{pred['p_away_win']:.2f}")
        
        # 计算准确率
        accuracy = accuracy_score(actuals, predictions)
        
        print(f"\n📈 测试集评估结果:")
        print(f"准确率: {accuracy:.3f} ({accuracy:.1%})")
        
        print("\n分类报告:")
        print(classification_report(actuals, predictions, 
                                  target_names=['客胜', '平局', '主胜']))
        
        print("混淆矩阵:")
        cm = confusion_matrix(actuals, predictions)
        print(cm)
        
        # 分析预测错误
        print(f"\n🔍 预测错误分析:")
        errors = []
        for i, (actual, pred) in enumerate(zip(actuals, predictions)):
            if actual != pred:
                match = test_matches[i]
                prob = probabilities[i]
                errors.append({
                    'match': match,
                    'actual': ['客胜', '平局', '主胜'][actual],
                    'predicted': ['客胜', '平局', '主胜'][pred],
                    'confidence': max(prob['p_home_win'], prob['p_draw'], prob['p_away_win'])
                })
        
        print(f"预测错误的比赛数: {len(errors)}")
        for error in errors[:5]:  # 显示前5个错误
            match = error['match']
            print(f"  {match['round']}轮 {match['home_team']} vs {match['away_team']}: "
                  f"实际{error['actual']}, 预测{error['predicted']}, 置信度{error['confidence']:.2f}")
        
        return accuracy
    
    def run_temporal_validation(self):
        """运行时间序列验证"""
        print("🎯 BTD模型时间序列验证")
        print("="*50)
        
        # 1. 加载并分割数据
        train_matches, test_matches = self.load_and_split_data()
        if not train_matches or not test_matches:
            return
        
        # 2. 在训练集上拟合模型
        self.fit_btd_model(train_matches)
        
        # 3. 在测试集上评估
        test_accuracy = self.evaluate_on_test_set(test_matches)
        
        # 4. 对比全数据集训练的结果
        print(f"\n📊 性能对比:")
        print(f"时间序列验证准确率: {test_accuracy:.1%}")
        print(f"(这是更真实的预测能力评估)")
        
        print(f"\n💡 总结:")
        print(f"✅ 时间序列验证完成")
        print(f"✅ 模型在未见过的未来比赛上的表现: {test_accuracy:.1%}")
        print(f"✅ 这比全数据集训练更能反映真实预测能力")
        
        return test_accuracy

def main():
    """主函数"""
    validator = BTDTemporalValidation()
    test_accuracy = validator.run_temporal_validation()
    
    print(f"\n🎉 验证完成!")
    print(f"真实预测能力: {test_accuracy:.1%}")

if __name__ == "__main__":
    main()
